import { NextResponse } from "next/server";

export async function POST(request) {
  try {
    const { mermaidCode, errorMessage, aiConfig, accessPassword, selectedModel } = await request.json();

    if (!mermaidCode) {
      return NextResponse.json(
        { error: "请提供需要修复的Mermaid代码" },
        { status: 400 }
      );
    }

    let finalConfig;

    // 步骤1: 检查是否有完整的aiConfig
    const hasCompleteAiConfig = aiConfig?.apiUrl && aiConfig?.apiKey && aiConfig?.modelName;

    if (hasCompleteAiConfig) {
      // 如果有完整的aiConfig，直接使用
      finalConfig = {
        apiUrl: aiConfig.apiUrl,
        apiKey: aiConfig.apiKey,
        modelName: aiConfig.modelName
      };
    } else {
      // 步骤2: 如果没有完整的aiConfig，则检验accessPassword
      if (accessPassword) {
        // 步骤3: 如果传入了accessPassword，验证是否有效
        const correctPassword = process.env.ACCESS_PASSWORD;
        const isPasswordValid = correctPassword && accessPassword === correctPassword;

        if (!isPasswordValid) {
          // 如果密码无效，直接报错
          return NextResponse.json({
            error: "访问密码无效"
          }, { status: 401 });
        }
      }

      // 如果没有传入accessPassword或者accessPassword有效，使用环境变量配置
      // 如果有选择的模型，使用选择的模型，否则使用默认模型
      finalConfig = {
        apiUrl: process.env.AI_API_URL,
        apiKey: process.env.AI_API_KEY,
        modelName: selectedModel || process.env.AI_MODEL_NAME
      };
    }

    // 检查最终配置是否完整
    if (!finalConfig.apiUrl || !finalConfig.apiKey || !finalConfig.modelName) {
      return NextResponse.json({
        error: "AI配置不完整，请在设置中配置API URL、API Key和模型名称"
      }, { status: 400 });
    }

    // 构建用户提示词，包含错误信息（如果有的话）
    let userPrompt = `请修复以下Mermaid代码中的问题。请返回修复后的代码，且不要返回其他的内容，并使用\`\`\`mermaid包裹。`;
    userPrompt += `
    mermaid代码为：
"${mermaidCode}"
`;

    // 如果有具体的错误信息，添加到提示词中
    if (errorMessage && errorMessage.trim()) {
      userPrompt += `
错误信息为：
"${errorMessage}"
`;
    }

    userPrompt += `你的主要修改思路是：
1. 分析错误信息，理解错误原因
2. 检查是否有特殊字符，如果有，使用引号包裹
3. 检查是否有关键字，如果有则使用别的字符代替
4. 修复其他问题
`;

    const messages = [
      {
        role: "system",
        content: "你是一个专业的Mermaid图表代码修复专家。你的任务是分析和修复Mermaid代码中的各种问题。",
      },
      {
        role: "user",
        content: userPrompt,
      },
    ];

    // 构建API URL
    const url = finalConfig.apiUrl.includes("v1") || finalConfig.apiUrl.includes("v3")
      ? `${finalConfig.apiUrl}/chat/completions`
      : `${finalConfig.apiUrl}/v1/chat/completions`;

    console.log('Using AI config for fix-mermaid:', {
      url,
      modelName: finalConfig.modelName,
      hasApiKey: !!finalConfig.apiKey,
    });

    // 发送请求到 AI API
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${finalConfig.apiKey}`,
      },
      body: JSON.stringify({
        model: finalConfig.modelName,
        messages,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("AI API Error:", response.status, errorText);
      throw new Error(`AI服务返回错误 (${response.status}): ${errorText || 'Unknown error'}`);
    }

    const completion = await response.json();
    const aiResponse = completion.choices[0]?.message?.content;

    if (!aiResponse) {
      throw new Error("AI服务返回空响应");
    }

    // 解析AI响应，提取修复后的代码
    const { fixedCode, suggestions } = parseAIResponse(aiResponse, mermaidCode);

    return NextResponse.json({
      fixedCode,
      suggestions,
      originalCode: mermaidCode
    });

  } catch (error) {
    console.error("修复Mermaid代码时出错:", error);
    
    return NextResponse.json(
      { 
        error: error.message || "修复代码时发生未知错误",
        details: process.env.NODE_ENV === 'development' ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * 解析AI响应，提取修复后的代码和建议
 */
function parseAIResponse(aiResponse, originalCode) {
  try {
    // 尝试提取代码块
    const codeBlockRegex = /```(?:mermaid)?\s*([\s\S]*?)\s*```/;
    const match = aiResponse.match(codeBlockRegex);
    
    let fixedCode = originalCode;
    let suggestions = [];

    if (match && match[1]) {
      fixedCode = match[1].trim();
    } else {
      // 如果没有找到代码块，尝试其他方式提取
      const lines = aiResponse.split('\n');
      const codeLines = lines.filter(line => 
        line.trim() && 
        !line.includes('修复') && 
        !line.includes('问题') &&
        !line.includes('说明') &&
        (line.includes('flowchart') || line.includes('graph') || line.includes('-->') || line.includes('[') || line.includes(']'))
      );
      
      if (codeLines.length > 0) {
        fixedCode = codeLines.join('\n').trim();
      }
    }

    // 提取修复建议
    const suggestionKeywords = ['修复', '问题', '改进', '优化', '建议'];
    const responseLines = aiResponse.split('\n');
    
    suggestions = responseLines
      .filter(line => suggestionKeywords.some(keyword => line.includes(keyword)))
      .map(line => line.trim())
      .filter(line => line.length > 0)
      .slice(0, 3); // 最多保留3条建议

    // 如果没有提取到建议，添加默认建议
    if (suggestions.length === 0) {
      suggestions = ["代码已通过AI分析和优化"];
    }

    return { fixedCode, suggestions };
  } catch (error) {
    console.error("解析AI响应时出错:", error);
    return { 
      fixedCode: originalCode, 
      suggestions: ["AI响应解析失败，请检查代码格式"] 
    };
  }
}

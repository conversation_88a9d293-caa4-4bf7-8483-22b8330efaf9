/**
 * Mermaid代码AI智能修复工具
 * 通过AI分析和修复Mermaid代码中的各种问题
 */

import { getAIConfig, getSavedPassword, getSelectedModel } from "./config-service";

/**
 * 使用AI自动修复Mermaid代码中的问题
 * @param {string} mermaidCode - 原始Mermaid代码
 * @param {string} errorMessage - 可选的错误信息，用于指导AI修复
 * @returns {Promise<{fixedCode: string, error: string|null, suggestions: string[]}>} - 修复结果
 */
export async function autoFixMermaidCode(mermaidCode, errorMessage = null) {
  if (!mermaidCode || typeof mermaidCode !== 'string') {
    return {
      fixedCode: mermaidCode,
      error: "无效的代码输入",
      suggestions: []
    };
  }

  try {
    // 获取AI配置
    const aiConfig = getAIConfig();
    const accessPassword = getSavedPassword();
    const selectedModel = getSelectedModel();

    const response = await fetch("/api/fix-mermaid", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        mermaidCode,
        errorMessage,
        aiConfig,
        accessPassword,
        selectedModel
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "修复代码时出错");
    }

    const data = await response.json();
    return {
      fixedCode: data.fixedCode || mermaidCode,
      error: null,
      suggestions: data.suggestions || []
    };
  } catch (error) {
    console.error("AI修复错误:", error);

  }
}

/**
 * 切换图表方向 (TD <-> LR)
 * @param {string} mermaidCode - 原始Mermaid代码
 * @returns {string} - 切换方向后的代码
 */
export function toggleMermaidDirection(mermaidCode) {
  if (!mermaidCode || typeof mermaidCode !== 'string') {
    return mermaidCode;
  }

  let result = mermaidCode;

  // 查找并替换方向定义
  // 匹配 flowchart TD 或 graph TD 等
  result = result.replace(/(flowchart|graph)\s+(TD|TB|LR|RL)/gi, (_, type, direction) => {
    const newDirection = (direction.toUpperCase() === 'TD' || direction.toUpperCase() === 'TB') ? 'LR' : 'TD';
    return `${type} ${newDirection}`;
  });

  // 如果没有找到方向定义，尝试在第一行添加
  if (!result.match(/(flowchart|graph)\s+(TD|TB|LR|RL)/i)) {
    const lines = result.split('\n');
    if (lines.length > 0 && lines[0].trim() !== '') {
      // 检查第一行是否是图表类型声明
      if (lines[0].match(/^(flowchart|graph)$/i)) {
        lines[0] = `${lines[0]} TD`;
      } else if (!lines[0].match(/^(flowchart|graph)/i)) {
        // 如果第一行不是图表声明，添加一个
        lines.unshift('flowchart TD');
      }
      result = lines.join('\n');
    }
  }

  return result;
}
